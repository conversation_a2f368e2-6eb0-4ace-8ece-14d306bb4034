import type { CollectionConfig } from 'payload'

const AffiliateLinks: CollectionConfig = {
  slug: 'affiliate-links',
  admin: { useAsTitle: 'slug' },
  fields: [
    {
      name: 'slug',
      type: 'text',
      unique: true,
      required: true,
    },
    {
      name: 'targetUrl',
      type: 'text',
      required: true,
    },
    {
      name: 'utmParams',
      type: 'json',
      admin: { description: 'UTM parameters (auto or manual)' },
      required: false,
    },
    {
      name: 'owner',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      filterOptions: {
        role: { equals: 'affiliate' },
      },
    },
    {
      name: 'campaign',
      type: 'text', // Change to relationship if campaign collection is added
      required: false,
      admin: { description: 'Campaign or deal (optional)' },
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Disabled', value: 'disabled' },
      ],
      defaultValue: 'active',
    },
  ],
};

export default AffiliateLinks; 