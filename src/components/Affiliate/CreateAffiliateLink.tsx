'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Copy, Link2, Plus, X } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

interface AffiliateLink {
  id: string
  name: string
  url: string
  utmSource: string
  utmMedium: string
  utmCampaign: string
  utmTerm?: string
  utmContent?: string
  description?: string
  createdAt: string
}

export function CreateAffiliateLink() {
  const { toast } = useToast()
  const [isCreating, setIsCreating] = useState(false)
  const [generatedLinks, setGeneratedLinks] = useState<AffiliateLink[]>([])
  const [formData, setFormData] = useState({
    name: '',
    baseUrl: '',
    utmSource: '',
    utmMedium: 'affiliate',
    utmCampaign: '',
    utmTerm: '',
    utmContent: '',
    description: '',
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const generateAffiliateLink = () => {
    const { baseUrl, utmSource, utmMedium, utmCampaign, utmTerm, utmContent } = formData
    
    if (!baseUrl || !utmSource || !utmCampaign) {
      toast({
        title: 'Missing Required Fields',
        description: 'Please fill in Base URL, UTM Source, and UTM Campaign.',
        variant: 'destructive',
      })
      return
    }

    const params = new URLSearchParams()
    params.append('utm_source', utmSource)
    params.append('utm_medium', utmMedium)
    params.append('utm_campaign', utmCampaign)
    if (utmTerm) params.append('utm_term', utmTerm)
    if (utmContent) params.append('utm_content', utmContent)

    const fullUrl = `${baseUrl}?${params.toString()}`
    
    const newLink: AffiliateLink = {
      id: Date.now().toString(),
      name: formData.name || `${utmSource}-${utmCampaign}`,
      url: fullUrl,
      utmSource,
      utmMedium,
      utmCampaign,
      utmTerm: utmTerm || undefined,
      utmContent: utmContent || undefined,
      description: formData.description || undefined,
      createdAt: new Date().toISOString(),
    }

    setGeneratedLinks(prev => [newLink, ...prev])
    
    toast({
      title: 'Affiliate Link Created',
      description: 'Your affiliate link has been generated successfully.',
    })

    // Reset form
    setFormData({
      name: '',
      baseUrl: '',
      utmSource: '',
      utmMedium: 'affiliate',
      utmCampaign: '',
      utmTerm: '',
      utmContent: '',
      description: '',
    })
  }

  const copyToClipboard = (url: string) => {
    navigator.clipboard.writeText(url)
    toast({
      title: 'Copied to Clipboard',
      description: 'Affiliate link has been copied to your clipboard.',
    })
  }

  const removeLink = (id: string) => {
    setGeneratedLinks(prev => prev.filter(link => link.id !== id))
  }

  return (
    <div className="space-y-6">
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link2 className="h-5 w-5" />
            Create Affiliate Link
          </CardTitle>
          <CardDescription>
            Generate trackable affiliate links with UTM parameters for marketing campaigns
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Link Name (Optional)</Label>
              <Input
                id="name"
                placeholder="e.g., Summer Campaign 2024"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="baseUrl">Base URL *</Label>
              <Input
                id="baseUrl"
                placeholder="https://orchestars.com/events/summer-2024"
                value={formData.baseUrl}
                onChange={(e) => handleInputChange('baseUrl', e.target.value)}
              />
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="utmSource">UTM Source *</Label>
              <Input
                id="utmSource"
                placeholder="e.g., facebook, google, newsletter"
                value={formData.utmSource}
                onChange={(e) => handleInputChange('utmSource', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="utmMedium">UTM Medium</Label>
              <Select value={formData.utmMedium} onValueChange={(value) => handleInputChange('utmMedium', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="affiliate">Affiliate</SelectItem>
                  <SelectItem value="social">Social</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="cpc">CPC</SelectItem>
                  <SelectItem value="banner">Banner</SelectItem>
                  <SelectItem value="referral">Referral</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="utmCampaign">UTM Campaign *</Label>
              <Input
                id="utmCampaign"
                placeholder="e.g., summer-promo, holiday-sale"
                value={formData.utmCampaign}
                onChange={(e) => handleInputChange('utmCampaign', e.target.value)}
              />
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="utmTerm">UTM Term (Optional)</Label>
              <Input
                id="utmTerm"
                placeholder="e.g., classical music, orchestra"
                value={formData.utmTerm}
                onChange={(e) => handleInputChange('utmTerm', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="utmContent">UTM Content (Optional)</Label>
              <Input
                id="utmContent"
                placeholder="e.g., banner-top, text-link"
                value={formData.utmContent}
                onChange={(e) => handleInputChange('utmContent', e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              placeholder="Add notes about this affiliate link..."
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
            />
          </div>

          <Button onClick={generateAffiliateLink} className="w-full" disabled={isCreating}>
            <Plus className="h-4 w-4 mr-2" />
            Generate Affiliate Link
          </Button>
        </CardContent>
      </Card>

      {generatedLinks.length > 0 && (
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Generated Links</CardTitle>
            <CardDescription>Your recently created affiliate links</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {generatedLinks.map((link) => (
                <div key={link.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <h4 className="font-medium">{link.name}</h4>
                      {link.description && (
                        <p className="text-sm text-muted-foreground">{link.description}</p>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeLink(link.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary">Source: {link.utmSource}</Badge>
                    <Badge variant="secondary">Medium: {link.utmMedium}</Badge>
                    <Badge variant="secondary">Campaign: {link.utmCampaign}</Badge>
                    {link.utmTerm && <Badge variant="outline">Term: {link.utmTerm}</Badge>}
                    {link.utmContent && <Badge variant="outline">Content: {link.utmContent}</Badge>}
                  </div>
                  
                  <div className="flex items-center gap-2 p-2 bg-muted rounded">
                    <code className="flex-1 text-sm break-all">{link.url}</code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(link.url)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <p className="text-xs text-muted-foreground">
                    Created: {new Date(link.createdAt).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
