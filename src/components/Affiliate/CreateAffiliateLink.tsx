'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Copy, Link2, Plus, X } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

interface AffiliateLink {
  id: number
  affiliateCode: string
  promotionCode?: string | null
  utmParams?: {
    source?: string
    medium?: string
    campaign?: string
    term?: string
    content?: string
  } | null
  targetLink?: string | null
  generatedUrl?: string
  status: 'active' | 'disabled'
  createdAt: string
}

interface CreateAffiliateLinkRequest {
  targetLink: string
  utmParams: {
    source: string
    medium: string
    campaign: string
    term?: string
    content?: string
  }
  event?: string | null
  promotionCode?: string
}

interface ApiResponse {
  success: boolean
  data?: AffiliateLink
  error?: string
  details?: Array<{ field: string; message: string }>
}

export function CreateAffiliateLink() {
  const { toast } = useToast()
  const [isCreating, setIsCreating] = useState(false)
  const [generatedLinks, setGeneratedLinks] = useState<AffiliateLink[]>([])
  const [formData, setFormData] = useState({
    targetLink: '',
    utmSource: '',
    utmMedium: 'affiliate',
    utmCampaign: '',
    utmTerm: '',
    utmContent: '',
    event: '',
    promotionCode: '',
  })
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const generateAffiliateLink = async () => {
    setIsCreating(true)
    setValidationErrors({})

    try {
      const requestData: CreateAffiliateLinkRequest = {
        targetLink: formData.targetLink,
        utmParams: {
          source: formData.utmSource,
          medium: formData.utmMedium,
          campaign: formData.utmCampaign,
          term: formData.utmTerm || undefined,
          content: formData.utmContent || undefined,
        },
        event: formData.event || null,
        promotionCode: formData.promotionCode || undefined,
      }

      const response = await fetch('/api/affiliate/link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })

      const result: ApiResponse = await response.json()

      if (!result.success) {
        if (result.details) {
          // Handle validation errors
          const errors: Record<string, string> = {}
          result.details.forEach(detail => {
            errors[detail.field] = detail.message
          })
          setValidationErrors(errors)

          toast({
            title: 'Validation Error',
            description: 'Please check the form for errors.',
            variant: 'destructive',
          })
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to create affiliate link.',
            variant: 'destructive',
          })
        }
        return
      }

      if (result.data) {
        setGeneratedLinks(prev => [result.data!, ...prev])

        toast({
          title: 'Affiliate Link Created',
          description: 'Your affiliate link has been generated successfully.',
        })

        // Reset form
        setFormData({
          targetLink: '',
          utmSource: '',
          utmMedium: 'affiliate',
          utmCampaign: '',
          utmTerm: '',
          utmContent: '',
          event: '',
          promotionCode: '',
        })
      }
    } catch (error) {
      console.error('Error creating affiliate link:', error)
      toast({
        title: 'Error',
        description: 'Failed to create affiliate link. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsCreating(false)
    }
  }

  const copyToClipboard = (url: string) => {
    navigator.clipboard.writeText(url)
    toast({
      title: 'Copied to Clipboard',
      description: 'Affiliate link has been copied to your clipboard.',
    })
  }



  const removeLink = (id: number) => {
    setGeneratedLinks(prev => prev.filter(link => link.id !== id))
  }

  return (
    <div className="space-y-6">
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link2 className="h-5 w-5" />
            Create Affiliate Link
          </CardTitle>
          <CardDescription>
            Generate trackable affiliate links with UTM parameters for marketing campaigns
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="targetLink">Target URL *</Label>
              <Input
                id="targetLink"
                placeholder="https://orchestars.com/events/summer-2024"
                value={formData.targetLink}
                onChange={(e) => handleInputChange('targetLink', e.target.value)}
                className={validationErrors.targetLink ? 'border-red-500' : ''}
              />
              {validationErrors.targetLink && (
                <p className="text-sm text-red-600">{validationErrors.targetLink}</p>
              )}
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="event">Target Event (Optional)</Label>
              <Select value={formData.event} onValueChange={(value) => handleInputChange('event', value)}>
                <SelectTrigger className={validationErrors.event ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select an event (optional)" />
                </SelectTrigger>
                <SelectContent className='bg-white'>
                  <SelectItem value="">No specific event</SelectItem>
                  <SelectItem value="summer-concert-2024">Summer Concert Series 2024</SelectItem>
                  <SelectItem value="classical-nights">Classical Nights</SelectItem>
                  <SelectItem value="holiday-special">Holiday Special</SelectItem>
                  <SelectItem value="orchestra-gala">Orchestra Gala</SelectItem>
                  <SelectItem value="spring-festival">Spring Festival</SelectItem>
                </SelectContent>
              </Select>
              {validationErrors.event && (
                <p className="text-sm text-red-600">{validationErrors.event}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="promotionCode">Promotion Code (Optional)</Label>
              <Input
                id="promotionCode"
                placeholder="e.g., SUMMER20, EARLYBIRD"
                value={formData.promotionCode}
                onChange={(e) => handleInputChange('promotionCode', e.target.value)}
                className={validationErrors.promotionCode ? 'border-red-500' : ''}
              />
              {validationErrors.promotionCode && (
                <p className="text-sm text-red-600">{validationErrors.promotionCode}</p>
              )}
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="utmSource">UTM Source *</Label>
              <Input
                id="utmSource"
                placeholder="e.g., facebook, google, newsletter"
                value={formData.utmSource}
                onChange={(e) => handleInputChange('utmSource', e.target.value)}
                className={validationErrors.utmSource ? 'border-red-500' : ''}
              />
              {validationErrors.utmSource && (
                <p className="text-sm text-red-600">{validationErrors.utmSource}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="utmMedium">UTM Medium</Label>
              <Select value={formData.utmMedium} onValueChange={(value) => handleInputChange('utmMedium', value)}>
                <SelectTrigger className={validationErrors.utmMedium ? 'border-red-500' : ''}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className='bg-white'>
                  <SelectItem value="affiliate">Affiliate</SelectItem>
                  <SelectItem value="social">Social</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="cpc">CPC</SelectItem>
                  <SelectItem value="banner">Banner</SelectItem>
                  <SelectItem value="referral">Referral</SelectItem>
                </SelectContent>
              </Select>
              {validationErrors.utmMedium && (
                <p className="text-sm text-red-600">{validationErrors.utmMedium}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="utmCampaign">UTM Campaign *</Label>
              <Input
                id="utmCampaign"
                placeholder="e.g., summer-promo, holiday-sale"
                value={formData.utmCampaign}
                onChange={(e) => handleInputChange('utmCampaign', e.target.value)}
                className={validationErrors.utmCampaign ? 'border-red-500' : ''}
              />
              {validationErrors.utmCampaign && (
                <p className="text-sm text-red-600">{validationErrors.utmCampaign}</p>
              )}
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="utmTerm">UTM Term (Optional)</Label>
              <Input
                id="utmTerm"
                placeholder="e.g., classical music, orchestra"
                value={formData.utmTerm}
                onChange={(e) => handleInputChange('utmTerm', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="utmContent">UTM Content (Optional)</Label>
              <Input
                id="utmContent"
                placeholder="e.g., banner-top, text-link"
                value={formData.utmContent}
                onChange={(e) => handleInputChange('utmContent', e.target.value)}
              />
            </div>
          </div>



          <Button onClick={generateAffiliateLink} className="w-full" disabled={isCreating}>
            <Plus className="h-4 w-4 mr-2" />
            Generate Affiliate Link
          </Button>
        </CardContent>
      </Card>

      {generatedLinks.length > 0 && (
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Generated Links</CardTitle>
            <CardDescription>Your recently created affiliate links</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {generatedLinks.map((link) => (
                <div key={link.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <h4 className="font-medium">Affiliate Code: {link.affiliateCode}</h4>
                      <p className="text-sm text-muted-foreground">Target: {link.targetLink}</p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeLink(link.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {link.utmParams?.source && <Badge variant="secondary">Source: {link.utmParams.source}</Badge>}
                    {link.utmParams?.medium && <Badge variant="secondary">Medium: {link.utmParams.medium}</Badge>}
                    {link.utmParams?.campaign && <Badge variant="secondary">Campaign: {link.utmParams.campaign}</Badge>}
                    {link.utmParams?.term && <Badge variant="outline">Term: {link.utmParams.term}</Badge>}
                    {link.utmParams?.content && <Badge variant="outline">Content: {link.utmParams.content}</Badge>}
                    {link.promotionCode && <Badge variant="outline">Promo: {link.promotionCode}</Badge>}
                  </div>

                  <div className="flex items-center gap-2 p-2 bg-muted rounded">
                    <code className="flex-1 text-sm break-all">{link.generatedUrl || link.targetLink}</code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(link.generatedUrl || link.targetLink || '')}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>

                  <p className="text-xs text-muted-foreground">
                    Created: {new Date(link.createdAt).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
